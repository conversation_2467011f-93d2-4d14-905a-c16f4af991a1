
import os
import pika
import time
import json
import threading
from dotenv import load_dotenv
from mq import connect_to_rabbitmq
from event_enum import event_dict
from get_recomendations import get_recomendations

load_dotenv()

# Global connection and channel for acknowledgments
current_connection = None
current_channel = None
connection_lock = threading.Lock()
heartbeat_timer = None

def send_heartbeat():
    """Send periodic heartbeats to keep connection alive during long operations"""
    global current_connection, heartbeat_timer

    try:
        with connection_lock:
            if current_connection and not current_connection.is_closed:
                current_connection.process_data_events(time_limit=0)
                # Schedule next heartbeat
                heartbeat_timer = threading.Timer(30.0, send_heartbeat)
                heartbeat_timer.start()
    except Exception as e:
        print(f"Error sending heartbeat: {e}")

def start_heartbeat():
    """Start the heartbeat timer for long-running operations"""
    global heartbeat_timer

    if heartbeat_timer:
        heartbeat_timer.cancel()

    heartbeat_timer = threading.Timer(30.0, send_heartbeat)
    heartbeat_timer.start()

def stop_heartbeat():
    """Stop the heartbeat timer"""
    global heartbeat_timer

    if heartbeat_timer:
        heartbeat_timer.cancel()
        heartbeat_timer = None

def ensure_connection():
    """Ensure we have a valid connection and channel for acknowledgments"""
    global current_connection, current_channel

    with connection_lock:
        if current_connection is None or current_connection.is_closed:
            current_connection = connect_to_rabbitmq()
            current_channel = current_connection.channel()
        elif current_channel is None or current_channel.is_closed:
            current_channel = current_connection.channel()

    return current_channel

def safe_ack(delivery_tag, original_channel):
    """Safely acknowledge a message, handling connection issues"""
    try:
        # First try with the original channel
        if original_channel and not original_channel.is_closed:
            original_channel.basic_ack(delivery_tag=delivery_tag)
            print(f"Message acknowledged successfully with original channel")
            return True
    except Exception as e:
        print(f"Failed to ack with original channel: {e}")

    try:
        # If original channel fails, try with a fresh connection
        ack_channel = ensure_connection()
        ack_channel.basic_ack(delivery_tag=delivery_tag)
        print(f"Message acknowledged successfully with fresh channel")
        return True
    except Exception as e:
        print(f"Failed to acknowledge message even with fresh connection: {e}")
        return False

def callback(ch, method, properties, body):
    params = json.loads(body.decode('utf-8'))
    event = params['event']

    # Start heartbeat for long-running operations
    start_heartbeat()

    try:
        if event == event_dict['PROCESS_RECOMMENDATIONS']:
            get_recomendations(params)
            print("Processing recomendations")
        print(f"Successfully processed event: {event}")

    except Exception as e:
        error_message = str(e)
        print(f"Error processing message: {error_message}")
    finally:
        # Stop heartbeat after processing
        stop_heartbeat()

 
    ack_success = safe_ack(method.delivery_tag, ch)
    if not ack_success:
        print(f"WARNING: Failed to acknowledge message for event {event}. Message may be redelivered.")



def start_consuming():
    global current_connection, current_channel

    while True:
        try:
            connection = connect_to_rabbitmq()
            channel = connection.channel()

            # Update global connection references
            with connection_lock:
                current_connection = connection
                current_channel = channel

            queue_name = os.getenv("MQ_ROUTE_KEY")
            channel.queue_declare(queue=queue_name, durable=True)

            # Set QoS to process one message at a time to prevent overwhelming
            channel.basic_qos(prefetch_count=1)

            channel.basic_consume(queue=queue_name, on_message_callback=callback, auto_ack=False)

            print("RabbitMQ consuming started. Waiting for messages...")
            channel.start_consuming()

        except pika.exceptions.StreamLostError as e:
            print(f"Stream connection lost: {e}. Reconnecting...")
        except pika.exceptions.AMQPConnectionError as e:
            print(f"AMQP connection error: {e}. Reconnecting...")
        except KeyboardInterrupt:
            print("Received interrupt signal. Shutting down gracefully...")
            break
        except Exception as e:
            print(f"Unexpected error: {e}. Reconnecting...")
        finally:
            # Stop any running heartbeat
            stop_heartbeat()

            # Clean up global references
            with connection_lock:
                current_channel = None
                current_connection = None

            # Close the connection if it exists
            if 'connection' in locals() and connection and connection.is_open:
                try:
                    connection.close()
                except Exception as e:
                    print(f"Error closing connection: {e}")
            # Wait before reconnecting
            time.sleep(5)


if __name__ == '__main__':
    start_consuming()