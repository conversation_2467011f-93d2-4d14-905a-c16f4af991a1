from transformers import pipeline
from data import coordination_mechanisms, processes, improved_project_resilience, community_satisfaction, monitoring_indicators, feedback_mechanisms, governance_rules, socio_ecological_challenges, stakeholders

# Load pre-trained transformer pipeline with specific model for production stability
classifier = pipeline(
    "zero-shot-classification",
    model="facebook/bart-large-mnli",
    revision="c626438"  # Pinned revision for reproducibility
)


# Function to find the top matches
def find_top_matches(project_description, category_items, top_n=3):
    scored_items = []
    for item, description in category_items:
        result = classifier(project_description, candidate_labels=[item])
        score = result['scores'][0]
        scored_items.append({"title":item, "description": description, "score": score})

    scored_items.sort(key=lambda x: x["score"], reverse=True)
    return scored_items[:top_n]


def assess_project_recommendation(project_description, top_n=3):
    results = {
        "Top Coordination Mechanisms": {
                'recommendations': find_top_matches(project_description, coordination_mechanisms, top_n),
                'references': []    
            },
        "Top Processes": {
                'recommendations': find_top_matches(project_description, processes, top_n),
                'references': []    
            },
        "Top Improved Project Resilience Factors": {
                'recommendations': find_top_matches(project_description, improved_project_resilience, top_n),
                'references': []    
            },
        "Top Community Satisfaction Factors": {
                'recommendations': find_top_matches(project_description, community_satisfaction, top_n),
                'references': []    
            },
        "Top Monitoring Indicators": {
                'recommendations': find_top_matches(project_description, monitoring_indicators, top_n),
                'references': []    
            },
        "Top Feedback Mechanisms": {
                'recommendations': find_top_matches(project_description, feedback_mechanisms, top_n),
                'references': []    
            },
        "Top Governance Rules": {
                'recommendations': find_top_matches(project_description, governance_rules, top_n),
                'references': []    
            },
        "Top Socio-ecological Challenges": {
                'recommendations': find_top_matches(project_description, socio_ecological_challenges, top_n),
                'references': []    
            },
        "Top Stakeholders": {
                'recommendations': find_top_matches(project_description, stakeholders, top_n),
                'references': []    
            },
    }
    return results

