import os
import fitz
import requests
import tempfile
import torch
from sentence_transformers import SentenceTransformer, util


# Load pre-trained sentence embedding model
model = SentenceTransformer('all-MiniLM-L6-v2')


class PDFProcessor:
    def __init__(self, pdf_file_link,  recommendations):
        self.pdf_file_link = pdf_file_link
        self.sentences_with_location = []  
        self.recommendations = recommendations 

    def process_pdf(self):
        """
        Process all PDFs from the provided external file links and group texts by country and year.
        """
        pdf_url = self.pdf_file_link
        temp_pdf_path = ''
        if not pdf_url:
            print("No PDF file link provided.")
            return

        filename = os.path.basename(pdf_url)
        print(f"\nProcessing {pdf_url}...")

        try:
            # Download the PDF to a temporary file
            with tempfile.NamedTemporaryFile(delete=False, suffix=".pdf") as temp_pdf:
                response = requests.get(pdf_url, stream=True, timeout=30)
                print(f"Download response status: {response.status_code}")
                if response.status_code == 200:
                    for chunk in response.iter_content(chunk_size=1024):
                        temp_pdf.write(chunk)
                    print(f"Successfully downloaded PDF to: {temp_pdf.name}")
                else:
                    print(f"Failed to download {filename}. HTTP Status Code: {response.status_code}")
                    return None

                temp_pdf_path = temp_pdf.name

            # Verify the file was created and has content
            if os.path.exists(temp_pdf_path):
                file_size = os.path.getsize(temp_pdf_path)
                print(f"Downloaded PDF file size: {file_size} bytes")
                if file_size == 0:
                    print("Downloaded PDF file is empty")
                    return None
            else:
                print(f"Temporary PDF file was not created: {temp_pdf_path}")
                return None

            # Extract text and year from the downloaded PDF
            self.sentences_with_location = self.extract_text_with_location(temp_pdf_path)


        except Exception as e:
            print(f"Failed to process {filename}. Error: {e}")

        finally:
            # Clean up the temporary file
            if temp_pdf_path and os.path.exists(temp_pdf_path):
                os.remove(temp_pdf_path)
                print(f"Cleaned up temporary file: {temp_pdf_path}")
                
    def extract_text_with_location(self, pdf):
        """
        Extracts text from a PDF file while tracking page numbers and line numbers.
        Returns a list of tuples: (sentence, full_line, page_number, line_number)
        """
        try:
            # Check if file exists
            if not os.path.exists(pdf):
                print(f"PDF file does not exist: {pdf}")
                return []

            doc = fitz.open(pdf)
            sentences_with_location = []

            for page_num in range(len(doc)):
                page = doc[page_num]
                text = page.get_text("text")  # Extract text from the page
                lines = text.split("\n")  # Split into individual lines

                for line_num, line in enumerate(lines, start=1):
                    sentences = line.split(". ")  # Simple sentence split
                    for sentence in sentences:
                        if sentence.strip():  # Avoid empty sentences
                            sentences_with_location.append((sentence.strip(), line.strip(), page_num + 1, line_num))

            doc.close()  # Properly close the document
            print(f"Successfully extracted {len(sentences_with_location)} sentences from PDF")
            return sentences_with_location

        except Exception as e:
            print(f"Failed to extract text with location. Error: {e}")
            return []
        
    def find_similar_sentences(self, query, top_n=3):
        """
        Finds the most similar sentences to the query and returns their locations with full lines.
        """
        # Check if we have any sentences to search through
        if not self.sentences_with_location:
            print(f"No sentences available for similarity search. Query: '{query}'")
            return []

        sentences = [entry[0] for entry in self.sentences_with_location]  # Extract only sentences

        # Additional check for empty sentences list
        if not sentences:
            print(f"No valid sentences extracted for similarity search. Query: '{query}'")
            return []

        try:
            sentence_embeddings = model.encode(sentences, convert_to_tensor=True)
            query_embedding = model.encode(query, convert_to_tensor=True)

            # Compute cosine similarity
            similarities = util.pytorch_cos_sim(query_embedding, sentence_embeddings)[0]

            # Ensure we don't request more results than available sentences
            actual_top_n = min(top_n, len(sentences))
            top_results = torch.topk(similarities, actual_top_n)

            # Retrieve matching sentences with page and line numbers
            top_matches = []
            for idx in top_results.indices:
                sentence, full_line, page_num, line_num = self.sentences_with_location[idx]
                top_matches.append({"sentence":sentence, "page": page_num, "line": line_num})

            return top_matches

        except Exception as e:
            print(f"Error during similarity search for query '{query}': {e}")
            return []
        # if top_matches:
        #     print(f"\nTop sentences related to '{query}':\n");
        #     for i, (sentence, full_line, page, line) in enumerate(top_matches, 1):
        #         print(f"{i}. Page {page}, Line {line}:")
        #         print(f"   Full Line: {full_line}")
        #         print(f"   Matched Sentence: {sentence}\n")
        # else:
        #     print(f"\nNo related sentences found for '{query}'.")
    
    def run (self):
        self.process_pdf()  
        print ("sentences_with_location")
        
        for category, items in self.recommendations.items():
            for recommendation in items['recommendations']:
                item = recommendation['title']
                description = recommendation['description']
                items['references'].extend(self.find_similar_sentences(f"{item}: {description}"))
                
        return self.recommendations
