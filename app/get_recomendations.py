import json
import os
from event_enum import event_dict
from mq import connect_to_rabbitmq
from lib.recommend_processor import assess_project_recommendation
from lib.pdf_processor import PDFProcessor




def get_recomendations(body):
    queue_name = os.getenv("MQ_CLIMAS_ODOO_ROUTE_KEY")
    project = body['project_id']
    recommendation_text = body['recommendation_text']
    pdf_link = body['attachment']
    output_result_count = body['output_result_count']
    
    print("recomendations started")
    try: 
        recommendations = assess_project_recommendation(recommendation_text, output_result_count)
        pdf_processor = PDFProcessor(pdf_link, recommendations)
        results = pdf_processor.run()   
       
        
        payload = json.dumps({
            "event": event_dict['RECOMMENDATIONS_RESULT'],
            "body": {
                "status": "completed",
                "project_id": project,
                "results": results
            }
        })
        connection = connect_to_rabbitmq()
        channel = connection.channel()
        channel.basic_publish(
                exchange="", 
                routing_key=queue_name, 
                body=payload,)

        connection.close()
        print("recomendations completed")
    except Exception as e:
        print(f"Failed to process PDF: {e}")
    