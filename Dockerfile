FROM python:3.11-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements.txt .

RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY app/ ./app/

COPY ./supervisor.conf /etc/supervisor/conf.d/
# Set environment variables
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1




# Run the application
CMD ["supervisord", "-n", "-c", "/etc/supervisor/conf.d/supervisor.conf"]
